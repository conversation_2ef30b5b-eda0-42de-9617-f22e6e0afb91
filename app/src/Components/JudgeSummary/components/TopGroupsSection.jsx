import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Checkbox,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Flag as FlagIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ToggleOff as ToggleOffIcon
} from '@mui/icons-material';

const TopGroupsSection = ({
  sortedGroups,
  isAutoSelectLoading,
  handleAutoSelectTopGroups,
  handleMovesOnChange
}) => {
  const [isBatchUnselectLoading, setIsBatchUnselectLoading] = useState(false);

  // Function to handle batch unselect of all top groups
  const handleBatchUnselectTopGroups = async () => {
    console.log('Batch unselecting all top groups');

    try {
      setIsBatchUnselectLoading(true);

      // Get all top groups that are currently selected to move on
      const topGroupsToUnselect = sortedGroups.filter(group =>
        group.isTopScore && group.movesOn && group.hasScore
      );

      if (topGroupsToUnselect.length === 0) {
        console.log('No top groups to unselect');
        return;
      }

      console.log(`Unselecting ${topGroupsToUnselect.length} top groups`);

      // Process each group sequentially
      for (const group of topGroupsToUnselect) {
        await handleMovesOnChange(group, false);
      }

      console.log('Successfully unselected all top groups');
    } catch (error) {
      console.error('Error batch unselecting top groups:', error);
    } finally {
      setIsBatchUnselectLoading(false);
    }
  };
  return (
    <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, color: '#3f51b5', display: 'flex', alignItems: 'center' }}>
        <FlagIcon sx={{ mr: 1 }} /> Top 50% Groups
      </Typography>

      <Typography variant="body2" sx={{ mb: 2 }}>
        Based on current scores, the following groups are in the top 50% and should move on to Phase 2.
        You can manually override these selections using the "Moves On" checkbox.
      </Typography>

      <Box sx={{ mb: 3 }}>
        {sortedGroups.filter(group => group.isTopScore).length > 0 ? (
          <Grid container spacing={1}>
            {sortedGroups
              .filter(group => group.isTopScore)
              .sort((a, b) => b.score - a.score)
              .map(group => (
                <Grid item xs={12} sm={6} md={4} key={group._id}>
                  <Box
                    sx={{
                      p: 1,
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: group.movesOn ? 'rgba(63, 81, 181, 0.1)' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(63, 81, 181, 0.05)'
                      }
                    }}
                  >
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        Group: {group.maskedName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Score: {group.score}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {/* Individual toggle checkbox */}
                      <Tooltip title={group.movesOn ? "Unselect this group" : "Select this group to move on"}>
                        <Checkbox
                          checked={Boolean(group.movesOn)}
                          onChange={(e) => handleMovesOnChange(group, e.target.checked)}
                          disabled={!group.hasScore}
                          size="small"
                          sx={{
                            color: '#3f51b5',
                            '&.Mui-checked': {
                              color: '#3f51b5',
                            },
                          }}
                        />
                      </Tooltip>

                      {/* Status chip */}
                      {group.movesOn ? (
                        <Chip
                          size="small"
                          label="Selected"
                          sx={{ backgroundColor: '#3f51b5', color: 'white' }}
                        />
                      ) : (
                        <Chip
                          size="small"
                          label="Not Selected"
                          variant="outlined"
                          sx={{ borderColor: '#3f51b5', color: '#3f51b5' }}
                        />
                      )}
                    </Box>
                  </Box>
                </Grid>
              ))
            }
          </Grid>
        ) : (
          <Typography variant="body1" sx={{ textAlign: 'center', color: 'text.secondary', fontStyle: 'italic' }}>
            No groups have been scored yet. Score groups to see the top 50%.
          </Typography>
        )}
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          {sortedGroups.filter(group => group.isTopScore).length} groups in top 50%
          {sortedGroups.filter(group => group.isTopScore && group.movesOn).length > 0 && (
            <span> • {sortedGroups.filter(group => group.isTopScore && group.movesOn).length} selected</span>
          )}
        </Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {/* Batch Unselect Button */}
          <Button
            variant="outlined"
            color="error"
            size="small"
            onClick={handleBatchUnselectTopGroups}
            disabled={
              sortedGroups.filter(group => group.isTopScore && group.movesOn).length === 0 ||
              isBatchUnselectLoading ||
              isAutoSelectLoading
            }
            startIcon={isBatchUnselectLoading ? null : <ToggleOffIcon />}
          >
            {isBatchUnselectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Unselecting...
              </Box>
            ) : (
              'Unselect All'
            )}
          </Button>

          {/* Auto-Select Button */}
          <Button
            variant="outlined"
            color="primary"
            size="small"
            onClick={() => handleAutoSelectTopGroups()}
            disabled={
              sortedGroups.filter(group => group.isTopScore && !group.movesOn).length === 0 ||
              isAutoSelectLoading ||
              isBatchUnselectLoading
            }
            startIcon={isAutoSelectLoading ? null : <CheckCircleIcon />}
          >
            {isAutoSelectLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Selecting...
              </Box>
            ) : (
              'Select All'
            )}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default TopGroupsSection;
